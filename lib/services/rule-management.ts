/**
 * Rule Management Service
 * 
 * High-level service for managing conditional link rules with transaction support,
 * validation, conflict resolution, and performance monitoring.
 * 
 * This service provides a comprehensive API for rule lifecycle management,
 * integrating with the existing rule engine, repositories, and validation systems.
 */

import { db } from '@/lib/db'
import { LinkRepository } from '@/lib/repositories/link'
import { LinkConditionRepository } from '@/lib/repositories/link-condition'
import { 
  EnhancedRuleEvaluator,
  RuleValidator,
  RuleConflictResolver,
  RulePerformanceMonitor,
  RuleErrorHandler,
  type ConditionalLinkWithConditions,
  type EnhancedLinkCondition,
  type RuleValidationResult,
  type ConflictDetectionResult,
  type ConflictResolution,
  type PerformanceAlert,
  type PerformanceStats,
} from '@/lib/rule-engine'
import { ConditionalVisitorContext } from '@/lib/utils/conditional-visitor-context'
import { AppError, ValidationError, ConflictError, PerformanceError } from '@/lib/errors'

/**
 * Rule operation result with comprehensive metadata
 */
export interface RuleOperationResult<T = any> {
  success: boolean
  data?: T
  errors: string[]
  warnings: string[]
  performanceMetrics: {
    operationTime: number
    databaseQueries: number
    validationTime: number
    conflictCheckTime: number
  }
  metadata: {
    operationId: string
    timestamp: Date
    affectedRules: string[]
    conflictsResolved: number
    optimizationsApplied: string[]
  }
}

/**
 * Rule lifecycle states
 */
export type RuleLifecycleState = 
  | 'draft'
  | 'validating'
  | 'active'
  | 'inactive'
  | 'conflicted'
  | 'deprecated'
  | 'archived'

/**
 * Rule management options
 */
export interface RuleManagementOptions {
  /** Enable automatic conflict resolution */
  autoResolveConflicts?: boolean
  
  /** Enable performance monitoring */
  enablePerformanceMonitoring?: boolean
  
  /** Enable transaction support */
  useTransactions?: boolean
  
  /** Validation strictness level */
  validationLevel?: 'strict' | 'moderate' | 'lenient'
  
  /** Enable rule optimization suggestions */
  enableOptimization?: boolean
  
  /** Maximum operation timeout in milliseconds */
  operationTimeout?: number
}

/**
 * Bulk rule operation configuration
 */
export interface BulkRuleOperation {
  operation: 'create' | 'update' | 'delete' | 'activate' | 'deactivate'
  rules: Partial<EnhancedLinkCondition>[]
  options?: RuleManagementOptions
}

/**
 * Rule template for common patterns
 */
export interface RuleTemplate {
  id: string
  name: string
  description: string
  category: 'referrer' | 'location' | 'device' | 'time' | 'mixed'
  template: Partial<EnhancedLinkCondition>
  variables: Record<string, any>
  usageCount: number
  lastUsed: Date
}

/**
 * Service health status
 */
export interface ServiceHealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy'
  uptime: number
  operationsPerformed: number
  averageResponseTime: number
  errorRate: number
  lastHealthCheck: Date
  issues: string[]
  recommendations: string[]
}

/**
 * Rule Management Service
 * 
 * Provides high-level operations for managing conditional link rules
 * with comprehensive validation, conflict resolution, and performance monitoring.
 */
export class RuleManagementService {
  private static instance: RuleManagementService
  private evaluator: EnhancedRuleEvaluator
  private performanceMonitor: RulePerformanceMonitor
  private errorHandler: RuleErrorHandler
  private operationCount: number = 0
  private startTime: Date = new Date()
  private healthStatus: ServiceHealthStatus

  private constructor(private options: RuleManagementOptions = {}) {
    this.evaluator = new EnhancedRuleEvaluator()
    this.performanceMonitor = RulePerformanceMonitor.getInstance()
    this.errorHandler = RuleErrorHandler.getInstance()
    
    // Initialize health status
    this.healthStatus = {
      status: 'healthy',
      uptime: 0,
      operationsPerformed: 0,
      averageResponseTime: 0,
      errorRate: 0,
      lastHealthCheck: new Date(),
      issues: [],
      recommendations: []
    }

    // Set up performance monitoring
    if (options.enablePerformanceMonitoring !== false) {
      this.setupPerformanceMonitoring()
    }
  }

  /**
   * Get singleton instance
   */
  public static getInstance(options?: RuleManagementOptions): RuleManagementService {
    if (!RuleManagementService.instance) {
      RuleManagementService.instance = new RuleManagementService(options)
    }
    return RuleManagementService.instance
  }

  // ===== HIGH-LEVEL RULE OPERATIONS =====

  /**
   * Create a new rule with comprehensive validation and conflict checking
   */
  public async createRule(
    linkId: string,
    ruleData: Partial<EnhancedLinkCondition>,
    options: RuleManagementOptions = {}
  ): Promise<RuleOperationResult<EnhancedLinkCondition>> {
    const operationId = this.generateOperationId()
    const startTime = performance.now()
    
    try {
      return await this.executeWithTransaction(async () => {
        // Validate rule data
        const validationResult = await this.validateRule(ruleData, options)
        if (!validationResult.isValid) {
          throw new ValidationError(`Rule validation failed: ${validationResult.errors.join(', ')}`)
        }

        // Check for conflicts
        const link = await this.getLinkWithConditions(linkId)
        const conflictResult = await this.checkRuleConflicts(link, ruleData)
        
        if (conflictResult.hasConflicts && !options.autoResolveConflicts) {
          const errorConflicts = conflictResult.conflicts.filter(c => c.severity === 'error')
          if (errorConflicts.length > 0) {
            throw new ConflictError(`Rule conflicts detected: ${errorConflicts.map(c => c.description).join('; ')}`)
          }
        }

        // Auto-resolve conflicts if enabled
        let resolvedRule = ruleData
        let conflictsResolved = 0
        if (options.autoResolveConflicts && conflictResult.hasConflicts) {
          const resolutions = RuleConflictResolver.generateResolutions(conflictResult.conflicts, link)
          const autoResolutions = resolutions.filter(r => r.automatic)
          
          for (const resolution of autoResolutions) {
            resolvedRule = await this.applyResolution(resolvedRule, resolution)
            conflictsResolved++
          }
        }

        // Create the rule
        const createdRule = await LinkConditionRepository.create({
          linkId,
          ...resolvedRule
        } as any)

        // Apply optimizations if enabled
        const optimizations: string[] = []
        if (options.enableOptimization) {
          const optimizedRule = await this.optimizeRule(createdRule)
          if (optimizedRule !== createdRule) {
            await LinkConditionRepository.update(createdRule.id, optimizedRule)
            optimizations.push('priority_optimization', 'performance_optimization')
          }
        }

        const operationTime = performance.now() - startTime
        this.recordOperation('create', operationTime, true)

        return {
          success: true,
          data: createdRule,
          errors: [],
          warnings: validationResult.warnings,
          performanceMetrics: {
            operationTime,
            databaseQueries: 2, // Estimated
            validationTime: validationResult.performanceImpact || 0,
            conflictCheckTime: 0 // TODO: Implement timing
          },
          metadata: {
            operationId,
            timestamp: new Date(),
            affectedRules: [createdRule.id],
            conflictsResolved,
            optimizationsApplied: optimizations
          }
        }
      }, options)
    } catch (error) {
      const operationTime = performance.now() - startTime
      this.recordOperation('create', operationTime, false)
      
      return this.handleOperationError(error as Error, operationId, {
        operation: 'create',
        linkId,
        ruleData
      })
    }
  }

  /**
   * Update an existing rule with validation and conflict checking
   */
  public async updateRule(
    ruleId: string,
    updates: Partial<EnhancedLinkCondition>,
    options: RuleManagementOptions = {}
  ): Promise<RuleOperationResult<EnhancedLinkCondition>> {
    const operationId = this.generateOperationId()
    const startTime = performance.now()
    
    try {
      return await this.executeWithTransaction(async () => {
        // Get existing rule
        const existingRule = await LinkConditionRepository.findById(ruleId)
        if (!existingRule) {
          throw new ValidationError(`Rule not found: ${ruleId}`)
        }

        // Merge updates with existing rule
        const updatedRuleData = { ...existingRule, ...updates }

        // Validate updated rule
        const validationResult = await this.validateRule(updatedRuleData, options)
        if (!validationResult.isValid) {
          throw new ValidationError(`Rule validation failed: ${validationResult.errors.join(', ')}`)
        }

        // Check for conflicts
        const link = await this.getLinkWithConditions(existingRule.linkId)
        const conflictResult = await this.checkRuleConflicts(link, updatedRuleData, ruleId)
        
        if (conflictResult.hasConflicts && !options.autoResolveConflicts) {
          const errorConflicts = conflictResult.conflicts.filter(c => c.severity === 'error')
          if (errorConflicts.length > 0) {
            throw new ConflictError(`Rule conflicts detected: ${errorConflicts.map(c => c.description).join('; ')}`)
          }
        }

        // Update the rule
        const updatedRule = await LinkConditionRepository.update(ruleId, updatedRuleData)

        const operationTime = performance.now() - startTime
        this.recordOperation('update', operationTime, true)

        return {
          success: true,
          data: updatedRule,
          errors: [],
          warnings: validationResult.warnings,
          performanceMetrics: {
            operationTime,
            databaseQueries: 3, // Estimated
            validationTime: validationResult.performanceImpact || 0,
            conflictCheckTime: 0
          },
          metadata: {
            operationId,
            timestamp: new Date(),
            affectedRules: [ruleId],
            conflictsResolved: 0,
            optimizationsApplied: []
          }
        }
      }, options)
    } catch (error) {
      const operationTime = performance.now() - startTime
      this.recordOperation('update', operationTime, false)
      
      return this.handleOperationError(error as Error, operationId, {
        operation: 'update',
        ruleId,
        updates
      })
    }
  }

  // ===== PRIVATE HELPER METHODS =====

  private generateOperationId(): string {
    return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private async executeWithTransaction<T>(
    operation: () => Promise<T>,
    options: RuleManagementOptions
  ): Promise<T> {
    if (options.useTransactions === false) {
      return await operation()
    }

    return await db.$transaction(async () => {
      return await operation()
    })
  }

  private async validateRule(
    ruleData: Partial<EnhancedLinkCondition>,
    options: RuleManagementOptions
  ): Promise<RuleValidationResult> {
    // Use the existing RuleValidator
    return RuleValidator.validateRule(ruleData.rules as any)
  }

  private async getLinkWithConditions(linkId: string): Promise<ConditionalLinkWithConditions> {
    const link = await LinkRepository.findById(linkId)
    if (!link) {
      throw new ValidationError(`Link not found: ${linkId}`)
    }

    const conditions = await LinkConditionRepository.findByLinkId(linkId)
    
    return {
      ...link,
      conditions: conditions as EnhancedLinkCondition[]
    } as ConditionalLinkWithConditions
  }

  private async checkRuleConflicts(
    link: ConditionalLinkWithConditions,
    newRule: Partial<EnhancedLinkCondition>,
    excludeRuleId?: string
  ): Promise<ConflictDetectionResult> {
    // Create a temporary link with the new rule added
    const tempConditions = [...link.conditions]
    if (excludeRuleId) {
      const index = tempConditions.findIndex(c => c.id === excludeRuleId)
      if (index >= 0) {
        tempConditions[index] = { ...tempConditions[index], ...newRule } as EnhancedLinkCondition
      }
    } else {
      tempConditions.push(newRule as EnhancedLinkCondition)
    }

    const tempLink = { ...link, conditions: tempConditions }
    return RuleConflictResolver.detectConflicts(tempLink)
  }

  private async applyResolution(
    rule: Partial<EnhancedLinkCondition>,
    resolution: ConflictResolution
  ): Promise<Partial<EnhancedLinkCondition>> {
    // Apply the suggested changes from the resolution
    const changes = resolution.suggestedChanges.find(c => c.conditionId === rule.id)
    if (changes) {
      return { ...rule, ...changes.changes }
    }
    return rule
  }

  private async optimizeRule(rule: EnhancedLinkCondition): Promise<EnhancedLinkCondition> {
    // TODO: Implement rule optimization logic
    // This could include priority adjustments, rule simplification, etc.
    return rule
  }

  private recordOperation(operation: string, duration: number, success: boolean): void {
    this.operationCount++
    
    // Update health status
    this.healthStatus.operationsPerformed++
    this.healthStatus.averageResponseTime = 
      (this.healthStatus.averageResponseTime * (this.operationCount - 1) + duration) / this.operationCount
    
    if (!success) {
      this.healthStatus.errorRate = 
        (this.healthStatus.errorRate * (this.operationCount - 1) + 1) / this.operationCount
    }
  }

  private handleOperationError(
    error: Error,
    operationId: string,
    context: Record<string, any>
  ): RuleOperationResult {
    return {
      success: false,
      errors: [error.message],
      warnings: [],
      performanceMetrics: {
        operationTime: 0,
        databaseQueries: 0,
        validationTime: 0,
        conflictCheckTime: 0
      },
      metadata: {
        operationId,
        timestamp: new Date(),
        affectedRules: [],
        conflictsResolved: 0,
        optimizationsApplied: []
      }
    }
  }

  private setupPerformanceMonitoring(): void {
    this.performanceMonitor.onAlert((alert: PerformanceAlert) => {
      this.healthStatus.issues.push(`Performance alert: ${alert.type}`)

      if (alert.severity === 'critical') {
        this.healthStatus.status = 'unhealthy'
      } else if (this.healthStatus.status === 'healthy') {
        this.healthStatus.status = 'degraded'
      }
    })
  }

  // ===== BULK OPERATIONS =====

  /**
   * Perform bulk rule operations with transaction support
   */
  public async performBulkOperation(
    operations: BulkRuleOperation[],
    options: RuleManagementOptions = {}
  ): Promise<RuleOperationResult<any[]>> {
    const operationId = this.generateOperationId()
    const startTime = performance.now()

    try {
      return await this.executeWithTransaction(async () => {
        const results: any[] = []
        const errors: string[] = []
        const warnings: string[] = []
        let conflictsResolved = 0
        const affectedRules: string[] = []

        for (const operation of operations) {
          try {
            switch (operation.operation) {
              case 'create':
                for (const rule of operation.rules) {
                  const result = await this.createRule(
                    rule.linkId!,
                    rule,
                    { ...options, ...operation.options }
                  )
                  if (result.success) {
                    results.push(result.data)
                    affectedRules.push(result.data!.id)
                    conflictsResolved += result.metadata.conflictsResolved
                  } else {
                    errors.push(...result.errors)
                  }
                  warnings.push(...result.warnings)
                }
                break

              case 'update':
                for (const rule of operation.rules) {
                  if (!rule.id) {
                    errors.push('Rule ID required for update operation')
                    continue
                  }
                  const result = await this.updateRule(
                    rule.id,
                    rule,
                    { ...options, ...operation.options }
                  )
                  if (result.success) {
                    results.push(result.data)
                    affectedRules.push(rule.id)
                  } else {
                    errors.push(...result.errors)
                  }
                  warnings.push(...result.warnings)
                }
                break

              case 'delete':
                for (const rule of operation.rules) {
                  if (!rule.id) {
                    errors.push('Rule ID required for delete operation')
                    continue
                  }
                  const result = await this.deleteRule(rule.id, options)
                  if (result.success) {
                    results.push({ id: rule.id, deleted: true })
                    affectedRules.push(rule.id)
                  } else {
                    errors.push(...result.errors)
                  }
                  warnings.push(...result.warnings)
                }
                break

              case 'activate':
              case 'deactivate':
                const isActive = operation.operation === 'activate'
                for (const rule of operation.rules) {
                  if (!rule.id) {
                    errors.push(`Rule ID required for ${operation.operation} operation`)
                    continue
                  }
                  const result = await this.updateRuleStatus(rule.id, isActive, options)
                  if (result.success) {
                    results.push(result.data)
                    affectedRules.push(rule.id)
                  } else {
                    errors.push(...result.errors)
                  }
                  warnings.push(...result.warnings)
                }
                break
            }
          } catch (error) {
            errors.push(`Bulk operation failed: ${(error as Error).message}`)
          }
        }

        const operationTime = performance.now() - startTime
        this.recordOperation('bulk', operationTime, errors.length === 0)

        return {
          success: errors.length === 0,
          data: results,
          errors,
          warnings,
          performanceMetrics: {
            operationTime,
            databaseQueries: operations.length * 2, // Estimated
            validationTime: 0,
            conflictCheckTime: 0
          },
          metadata: {
            operationId,
            timestamp: new Date(),
            affectedRules,
            conflictsResolved,
            optimizationsApplied: []
          }
        }
      }, options)
    } catch (error) {
      const operationTime = performance.now() - startTime
      this.recordOperation('bulk', operationTime, false)

      return this.handleOperationError(error as Error, operationId, {
        operation: 'bulk',
        operations
      })
    }
  }

  /**
   * Delete a rule with cascade handling
   */
  public async deleteRule(
    ruleId: string,
    options: RuleManagementOptions = {}
  ): Promise<RuleOperationResult<{ id: string; deleted: boolean }>> {
    const operationId = this.generateOperationId()
    const startTime = performance.now()

    try {
      return await this.executeWithTransaction(async () => {
        // Check if rule exists
        const existingRule = await LinkConditionRepository.findById(ruleId)
        if (!existingRule) {
          throw new ValidationError(`Rule not found: ${ruleId}`)
        }

        // Delete the rule
        await LinkConditionRepository.delete(ruleId)

        const operationTime = performance.now() - startTime
        this.recordOperation('delete', operationTime, true)

        return {
          success: true,
          data: { id: ruleId, deleted: true },
          errors: [],
          warnings: [],
          performanceMetrics: {
            operationTime,
            databaseQueries: 2,
            validationTime: 0,
            conflictCheckTime: 0
          },
          metadata: {
            operationId,
            timestamp: new Date(),
            affectedRules: [ruleId],
            conflictsResolved: 0,
            optimizationsApplied: []
          }
        }
      }, options)
    } catch (error) {
      const operationTime = performance.now() - startTime
      this.recordOperation('delete', operationTime, false)

      return this.handleOperationError(error as Error, operationId, {
        operation: 'delete',
        ruleId
      })
    }
  }

  /**
   * Update rule status (activate/deactivate)
   */
  public async updateRuleStatus(
    ruleId: string,
    isActive: boolean,
    options: RuleManagementOptions = {}
  ): Promise<RuleOperationResult<EnhancedLinkCondition>> {
    return this.updateRule(ruleId, { isActive }, options)
  }

  // ===== RULE LIFECYCLE MANAGEMENT =====

  /**
   * Get rule lifecycle state
   */
  public async getRuleLifecycleState(ruleId: string): Promise<RuleLifecycleState> {
    const rule = await LinkConditionRepository.findById(ruleId)
    if (!rule) {
      return 'archived'
    }

    if (!rule.isActive) {
      return 'inactive'
    }

    // Check for conflicts
    const link = await this.getLinkWithConditions(rule.linkId)
    const conflictResult = RuleConflictResolver.detectConflicts(link)

    if (conflictResult.hasConflicts) {
      const ruleConflicts = conflictResult.conflicts.filter(c =>
        c.affectedRules.includes(ruleId)
      )
      if (ruleConflicts.some(c => c.severity === 'error')) {
        return 'conflicted'
      }
    }

    return 'active'
  }

  /**
   * Transition rule to a new lifecycle state
   */
  public async transitionRuleState(
    ruleId: string,
    targetState: RuleLifecycleState,
    options: RuleManagementOptions = {}
  ): Promise<RuleOperationResult<EnhancedLinkCondition>> {
    const operationId = this.generateOperationId()
    const startTime = performance.now()

    try {
      const currentState = await this.getRuleLifecycleState(ruleId)

      // Validate state transition
      if (!this.isValidStateTransition(currentState, targetState)) {
        throw new ValidationError(`Invalid state transition from ${currentState} to ${targetState}`)
      }

      // Apply state transition
      let updates: Partial<EnhancedLinkCondition> = {}

      switch (targetState) {
        case 'active':
          updates.isActive = true
          break
        case 'inactive':
          updates.isActive = false
          break
        case 'archived':
          updates.isActive = false
          // Could add archived timestamp or other metadata
          break
        default:
          throw new ValidationError(`Cannot directly transition to state: ${targetState}`)
      }

      const result = await this.updateRule(ruleId, updates, options)

      if (result.success) {
        result.metadata.optimizationsApplied.push(`state_transition:${currentState}->${targetState}`)
      }

      return result
    } catch (error) {
      const operationTime = performance.now() - startTime
      this.recordOperation('state_transition', operationTime, false)

      return this.handleOperationError(error as Error, operationId, {
        operation: 'state_transition',
        ruleId,
        targetState
      })
    }
  }

  private isValidStateTransition(from: RuleLifecycleState, to: RuleLifecycleState): boolean {
    const validTransitions: Record<RuleLifecycleState, RuleLifecycleState[]> = {
      draft: ['active', 'archived'],
      validating: ['active', 'inactive', 'archived'],
      active: ['inactive', 'conflicted', 'deprecated', 'archived'],
      inactive: ['active', 'archived'],
      conflicted: ['active', 'inactive', 'archived'],
      deprecated: ['archived'],
      archived: ['active'] // Can reactivate archived rules
    }

    return validTransitions[from]?.includes(to) || false
  }

  // ===== SERVICE PERFORMANCE MONITORING =====

  /**
   * Get comprehensive service health status
   */
  public getServiceHealth(): ServiceHealthStatus {
    const now = new Date()
    this.healthStatus.uptime = now.getTime() - this.startTime.getTime()
    this.healthStatus.lastHealthCheck = now

    // Update recommendations based on current status
    this.healthStatus.recommendations = this.generateHealthRecommendations()

    return { ...this.healthStatus }
  }

  /**
   * Perform service health check
   */
  public async performHealthCheck(): Promise<ServiceHealthStatus> {
    const startTime = performance.now()

    try {
      // Test database connectivity
      await db.$queryRaw`SELECT 1`

      // Test rule evaluation performance
      const testContext = this.createTestVisitorContext()
      const testLink = await this.createTestLink()

      const evaluationStart = performance.now()
      await this.evaluator.evaluateLink(testLink, testContext)
      const evaluationTime = performance.now() - evaluationStart

      // Update health metrics
      if (evaluationTime > 1000) { // 1 second threshold
        this.healthStatus.issues.push('Slow rule evaluation detected')
        this.healthStatus.status = 'degraded'
      }

      // Check error rate
      if (this.healthStatus.errorRate > 0.05) { // 5% error rate threshold
        this.healthStatus.issues.push('High error rate detected')
        this.healthStatus.status = 'degraded'
      }

      // Check if status should be healthy
      if (this.healthStatus.issues.length === 0) {
        this.healthStatus.status = 'healthy'
      }

    } catch (error) {
      this.healthStatus.status = 'unhealthy'
      this.healthStatus.issues.push(`Health check failed: ${(error as Error).message}`)
    }

    const healthCheckTime = performance.now() - startTime
    this.recordOperation('health_check', healthCheckTime, this.healthStatus.status !== 'unhealthy')

    return this.getServiceHealth()
  }

  /**
   * Get service performance statistics
   */
  public getPerformanceStats(): PerformanceStats & {
    serviceUptime: number
    totalOperations: number
    operationsPerSecond: number
  } {
    const baseStats = this.performanceMonitor.getPerformanceStats()
    const uptime = Date.now() - this.startTime.getTime()

    return {
      ...baseStats,
      serviceUptime: uptime,
      totalOperations: this.operationCount,
      operationsPerSecond: this.operationCount / (uptime / 1000)
    }
  }

  /**
   * Reset service statistics
   */
  public resetStats(): void {
    this.operationCount = 0
    this.startTime = new Date()
    this.healthStatus.operationsPerformed = 0
    this.healthStatus.averageResponseTime = 0
    this.healthStatus.errorRate = 0
    this.healthStatus.issues = []
    this.healthStatus.status = 'healthy'
  }

  /**
   * Subscribe to service alerts
   */
  public onServiceAlert(callback: (alert: PerformanceAlert & { serviceLevel: boolean }) => void): void {
    this.performanceMonitor.onAlert((alert) => {
      callback({ ...alert, serviceLevel: true })
    })
  }

  // ===== RULE OPTIMIZATION AND SUGGESTIONS =====

  /**
   * Analyze rules and provide optimization suggestions
   */
  public async analyzeRulesForOptimization(linkId: string): Promise<{
    suggestions: string[]
    potentialImprovements: {
      type: 'performance' | 'conflict' | 'redundancy' | 'priority'
      description: string
      impact: 'low' | 'medium' | 'high'
      autoFixable: boolean
    }[]
    estimatedPerformanceGain: number
  }> {
    const link = await this.getLinkWithConditions(linkId)
    const suggestions: string[] = []
    const potentialImprovements: any[] = []
    let estimatedPerformanceGain = 0

    // Check for rule conflicts
    const conflictResult = RuleConflictResolver.detectConflicts(link)
    if (conflictResult.hasConflicts) {
      suggestions.push('Resolve rule conflicts to improve evaluation performance')
      potentialImprovements.push({
        type: 'conflict',
        description: `${conflictResult.conflicts.length} conflicts detected`,
        impact: 'high',
        autoFixable: true
      })
      estimatedPerformanceGain += 20 // 20% improvement estimate
    }

    // Check for redundant rules
    const redundantRules = this.findRedundantRules(link.conditions)
    if (redundantRules.length > 0) {
      suggestions.push('Remove or consolidate redundant rules')
      potentialImprovements.push({
        type: 'redundancy',
        description: `${redundantRules.length} redundant rules found`,
        impact: 'medium',
        autoFixable: true
      })
      estimatedPerformanceGain += 10 // 10% improvement estimate
    }

    // Check priority optimization
    const priorityIssues = this.analyzePriorityOptimization(link.conditions)
    if (priorityIssues.length > 0) {
      suggestions.push('Optimize rule priorities for better performance')
      potentialImprovements.push({
        type: 'priority',
        description: 'Rule priorities can be optimized',
        impact: 'medium',
        autoFixable: true
      })
      estimatedPerformanceGain += 15 // 15% improvement estimate
    }

    return {
      suggestions,
      potentialImprovements,
      estimatedPerformanceGain: Math.min(estimatedPerformanceGain, 50) // Cap at 50%
    }
  }

  /**
   * Auto-optimize rules for a link
   */
  public async optimizeRulesForLink(
    linkId: string,
    options: RuleManagementOptions = {}
  ): Promise<RuleOperationResult<{
    optimizedRules: EnhancedLinkCondition[]
    improvementsApplied: string[]
    performanceGain: number
  }>> {
    const operationId = this.generateOperationId()
    const startTime = performance.now()

    try {
      return await this.executeWithTransaction(async () => {
        const link = await this.getLinkWithConditions(linkId)
        const originalConditions = [...link.conditions]
        let optimizedConditions = [...link.conditions]
        const improvementsApplied: string[] = []

        // Apply conflict resolutions
        const conflictResult = RuleConflictResolver.detectConflicts(link)
        if (conflictResult.hasConflicts) {
          const resolutions = RuleConflictResolver.generateResolutions(conflictResult.conflicts, link)
          const autoResolutions = resolutions.filter(r => r.automatic)

          for (const resolution of autoResolutions) {
            // Apply resolution logic here
            improvementsApplied.push(`conflict_resolution:${resolution.type}`)
          }
        }

        // Remove redundant rules
        const redundantRules = this.findRedundantRules(optimizedConditions)
        if (redundantRules.length > 0) {
          optimizedConditions = optimizedConditions.filter(rule =>
            !redundantRules.includes(rule.id)
          )
          improvementsApplied.push(`removed_redundant:${redundantRules.length}`)
        }

        // Optimize priorities
        optimizedConditions = this.optimizePriorities(optimizedConditions)
        if (this.prioritiesChanged(originalConditions, optimizedConditions)) {
          improvementsApplied.push('priority_optimization')
        }

        // Update rules in database
        for (const condition of optimizedConditions) {
          await LinkConditionRepository.update(condition.id, condition)
        }

        // Delete removed rules
        for (const ruleId of redundantRules) {
          await LinkConditionRepository.delete(ruleId)
        }

        const operationTime = performance.now() - startTime
        this.recordOperation('optimize', operationTime, true)

        // Calculate performance gain estimate
        const performanceGain = this.calculatePerformanceGain(improvementsApplied)

        return {
          success: true,
          data: {
            optimizedRules: optimizedConditions,
            improvementsApplied,
            performanceGain
          },
          errors: [],
          warnings: [],
          performanceMetrics: {
            operationTime,
            databaseQueries: optimizedConditions.length + redundantRules.length,
            validationTime: 0,
            conflictCheckTime: 0
          },
          metadata: {
            operationId,
            timestamp: new Date(),
            affectedRules: optimizedConditions.map(r => r.id),
            conflictsResolved: conflictResult.conflicts.length,
            optimizationsApplied: improvementsApplied
          }
        }
      }, options)
    } catch (error) {
      const operationTime = performance.now() - startTime
      this.recordOperation('optimize', operationTime, false)

      return this.handleOperationError(error as Error, operationId, {
        operation: 'optimize',
        linkId
      })
    }
  }

  // ===== PRIVATE OPTIMIZATION HELPERS =====

  private generateHealthRecommendations(): string[] {
    const recommendations: string[] = []

    if (this.healthStatus.errorRate > 0.05) {
      recommendations.push('Consider reviewing rule configurations to reduce error rate')
    }

    if (this.healthStatus.averageResponseTime > 500) {
      recommendations.push('Optimize rule complexity to improve response times')
    }

    if (this.healthStatus.issues.length > 5) {
      recommendations.push('Address accumulated issues to improve service stability')
    }

    return recommendations
  }

  private createTestVisitorContext(): ConditionalVisitorContext {
    // Create a minimal test context for health checks
    return {
      referrer: 'https://test.com',
      userAgent: 'test-agent',
      ipAddress: '127.0.0.1',
      country: 'US',
      device: {
        type: 'desktop',
        platform: 'test',
        browser: 'test'
      },
      timestamp: new Date()
    }
  }

  private async createTestLink(): Promise<ConditionalLinkWithConditions> {
    // Create a minimal test link for health checks
    return {
      id: 'test-link',
      title: 'Test Link',
      url: 'https://test.com',
      isVisible: true,
      order: 0,
      isScheduled: false,
      hasConditions: false,
      conditions: [],
      defaultBehavior: 'show'
    }
  }

  private findRedundantRules(conditions: EnhancedLinkCondition[]): string[] {
    const redundantRules: string[] = []

    // Simple redundancy check - rules with identical configurations
    for (let i = 0; i < conditions.length; i++) {
      for (let j = i + 1; j < conditions.length; j++) {
        const rule1 = conditions[i]
        const rule2 = conditions[j]

        if (rule1.type === rule2.type &&
            JSON.stringify(rule1.rules) === JSON.stringify(rule2.rules) &&
            JSON.stringify(rule1.action) === JSON.stringify(rule2.action)) {
          redundantRules.push(rule2.id) // Keep the first one, mark second as redundant
        }
      }
    }

    return redundantRules
  }

  private analyzePriorityOptimization(conditions: EnhancedLinkCondition[]): string[] {
    const issues: string[] = []

    // Check for gaps in priorities
    const priorities = conditions.map(c => c.priority).sort((a, b) => a - b)
    for (let i = 1; i < priorities.length; i++) {
      if (priorities[i] - priorities[i - 1] > 10) {
        issues.push('Large gaps in priority values detected')
        break
      }
    }

    // Check for duplicate priorities
    const duplicatePriorities = priorities.filter((p, i) => priorities.indexOf(p) !== i)
    if (duplicatePriorities.length > 0) {
      issues.push('Duplicate priority values detected')
    }

    return issues
  }

  private optimizePriorities(conditions: EnhancedLinkCondition[]): EnhancedLinkCondition[] {
    // Sort by current priority and reassign sequential priorities
    const sorted = [...conditions].sort((a, b) => b.priority - a.priority)

    return sorted.map((condition, index) => ({
      ...condition,
      priority: (sorted.length - index) * 10 // 10, 20, 30, etc.
    }))
  }

  private prioritiesChanged(
    original: EnhancedLinkCondition[],
    optimized: EnhancedLinkCondition[]
  ): boolean {
    if (original.length !== optimized.length) return true

    for (let i = 0; i < original.length; i++) {
      const originalRule = original.find(r => r.id === optimized[i].id)
      if (!originalRule || originalRule.priority !== optimized[i].priority) {
        return true
      }
    }

    return false
  }

  private calculatePerformanceGain(improvements: string[]): number {
    let gain = 0

    for (const improvement of improvements) {
      if (improvement.startsWith('conflict_resolution')) gain += 20
      if (improvement.startsWith('removed_redundant')) gain += 10
      if (improvement === 'priority_optimization') gain += 15
    }

    return Math.min(gain, 50) // Cap at 50%
  }

  // ===== ADVANCED VALIDATION AND CONFLICT RESOLUTION =====

  /**
   * Comprehensive rule validation with semantic analysis
   */
  public async validateRuleComprehensive(
    ruleData: Partial<EnhancedLinkCondition>,
    context: {
      linkId: string
      existingRules?: EnhancedLinkCondition[]
      validationLevel?: 'strict' | 'moderate' | 'lenient'
    }
  ): Promise<RuleValidationResult & {
    semanticIssues: string[]
    performanceWarnings: string[]
    securityConcerns: string[]
    compatibilityIssues: string[]
    suggestions: string[]
  }> {
    const startTime = performance.now()

    // Basic validation using existing validator
    const basicValidation = await this.validateRule(ruleData, {
      validationLevel: context.validationLevel || 'moderate'
    })

    // Extended validation results
    const semanticIssues: string[] = []
    const performanceWarnings: string[] = []
    const securityConcerns: string[] = []
    const compatibilityIssues: string[] = []
    const suggestions: string[] = []

    // Semantic validation
    if (ruleData.type && ruleData.rules) {
      const semanticResult = await this.validateRuleSemantics(ruleData.type, ruleData.rules)
      semanticIssues.push(...semanticResult.issues)
      suggestions.push(...semanticResult.suggestions)
    }

    // Performance validation
    if (ruleData.rules) {
      const performanceResult = this.validateRulePerformance(ruleData.rules)
      performanceWarnings.push(...performanceResult.warnings)
      suggestions.push(...performanceResult.suggestions)
    }

    // Security validation
    if (ruleData.rules) {
      const securityResult = this.validateRuleSecurity(ruleData.rules)
      securityConcerns.push(...securityResult.concerns)
      suggestions.push(...securityResult.suggestions)
    }

    // Compatibility validation
    if (context.existingRules) {
      const compatibilityResult = this.validateRuleCompatibility(ruleData, context.existingRules)
      compatibilityIssues.push(...compatibilityResult.issues)
      suggestions.push(...compatibilityResult.suggestions)
    }

    const validationTime = performance.now() - startTime

    return {
      ...basicValidation,
      semanticIssues,
      performanceWarnings,
      securityConcerns,
      compatibilityIssues,
      suggestions: [...new Set(suggestions)], // Remove duplicates
      performanceImpact: validationTime
    }
  }

  /**
   * Automatic conflict resolution with multiple strategies
   */
  public async resolveConflictsAutomatically(
    link: ConditionalLinkWithConditions,
    strategy: 'conservative' | 'aggressive' | 'balanced' = 'balanced'
  ): Promise<{
    resolvedConflicts: ConflictResolution[]
    updatedRules: EnhancedLinkCondition[]
    unresolvedConflicts: ConflictResolution[]
    resolutionSummary: string
  }> {
    const conflictResult = RuleConflictResolver.detectConflicts(link)

    if (!conflictResult.hasConflicts) {
      return {
        resolvedConflicts: [],
        updatedRules: link.conditions,
        unresolvedConflicts: [],
        resolutionSummary: 'No conflicts detected'
      }
    }

    const resolutions = RuleConflictResolver.generateResolutions(conflictResult.conflicts, link)
    const resolvedConflicts: ConflictResolution[] = []
    const unresolvedConflicts: ConflictResolution[] = []
    let updatedRules = [...link.conditions]

    for (const resolution of resolutions) {
      const shouldApply = this.shouldApplyResolution(resolution, strategy)

      if (shouldApply && resolution.automatic) {
        // Apply the resolution
        for (const change of resolution.suggestedChanges) {
          const ruleIndex = updatedRules.findIndex(r => r.id === change.conditionId)
          if (ruleIndex >= 0) {
            updatedRules[ruleIndex] = { ...updatedRules[ruleIndex], ...change.changes }
          }
        }
        resolvedConflicts.push(resolution)
      } else {
        unresolvedConflicts.push(resolution)
      }
    }

    const resolutionSummary = this.generateResolutionSummary(resolvedConflicts, unresolvedConflicts)

    return {
      resolvedConflicts,
      updatedRules,
      unresolvedConflicts,
      resolutionSummary
    }
  }

  /**
   * Generate optimization suggestions based on rule analysis
   */
  public async generateOptimizationSuggestions(
    linkId: string,
    analysisDepth: 'basic' | 'detailed' | 'comprehensive' = 'detailed'
  ): Promise<{
    suggestions: {
      category: 'performance' | 'maintainability' | 'reliability' | 'security'
      priority: 'low' | 'medium' | 'high' | 'critical'
      title: string
      description: string
      impact: string
      effort: 'low' | 'medium' | 'high'
      autoApplicable: boolean
      estimatedGain: number
    }[]
    overallScore: number
    recommendedActions: string[]
  }> {
    const link = await this.getLinkWithConditions(linkId)
    const suggestions: any[] = []
    let overallScore = 100 // Start with perfect score

    // Performance analysis
    const performanceAnalysis = await this.analyzePerformanceOptimizations(link)
    suggestions.push(...performanceAnalysis.suggestions)
    overallScore -= performanceAnalysis.scorePenalty

    // Maintainability analysis
    if (analysisDepth !== 'basic') {
      const maintainabilityAnalysis = this.analyzeMaintainabilityOptimizations(link)
      suggestions.push(...maintainabilityAnalysis.suggestions)
      overallScore -= maintainabilityAnalysis.scorePenalty
    }

    // Reliability analysis
    if (analysisDepth === 'comprehensive') {
      const reliabilityAnalysis = this.analyzeReliabilityOptimizations(link)
      suggestions.push(...reliabilityAnalysis.suggestions)
      overallScore -= reliabilityAnalysis.scorePenalty

      // Security analysis
      const securityAnalysis = this.analyzeSecurityOptimizations(link)
      suggestions.push(...securityAnalysis.suggestions)
      overallScore -= securityAnalysis.scorePenalty
    }

    // Sort suggestions by priority and impact
    suggestions.sort((a, b) => {
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })

    // Generate recommended actions
    const recommendedActions = suggestions
      .filter(s => s.priority === 'high' || s.priority === 'critical')
      .slice(0, 5) // Top 5 recommendations
      .map(s => s.title)

    return {
      suggestions,
      overallScore: Math.max(0, overallScore),
      recommendedActions
    }
  }

  /**
   * Batch validation for multiple rules
   */
  public async validateRulesBatch(
    rules: Array<{ linkId: string; ruleData: Partial<EnhancedLinkCondition> }>,
    options: RuleManagementOptions = {}
  ): Promise<{
    validationResults: Array<{
      linkId: string
      ruleData: Partial<EnhancedLinkCondition>
      validation: RuleValidationResult
      crossRuleIssues: string[]
    }>
    globalIssues: string[]
    batchSummary: {
      totalRules: number
      validRules: number
      invalidRules: number
      warningCount: number
      errorCount: number
    }
  }> {
    const validationResults: any[] = []
    const globalIssues: string[] = []
    let validRules = 0
    let invalidRules = 0
    let warningCount = 0
    let errorCount = 0

    // Validate each rule individually
    for (const { linkId, ruleData } of rules) {
      try {
        const existingRules = await LinkConditionRepository.findByLinkId(linkId)
        const validation = await this.validateRuleComprehensive(ruleData, {
          linkId,
          existingRules: existingRules as EnhancedLinkCondition[],
          validationLevel: options.validationLevel
        })

        // Check for cross-rule issues
        const crossRuleIssues = this.detectCrossRuleIssues(ruleData, rules)

        validationResults.push({
          linkId,
          ruleData,
          validation,
          crossRuleIssues
        })

        if (validation.isValid) {
          validRules++
        } else {
          invalidRules++
        }

        warningCount += validation.warnings.length
        errorCount += validation.errors.length

      } catch (error) {
        globalIssues.push(`Failed to validate rule for link ${linkId}: ${(error as Error).message}`)
        invalidRules++
      }
    }

    // Detect global patterns and issues
    const patternIssues = this.detectGlobalPatterns(rules)
    globalIssues.push(...patternIssues)

    return {
      validationResults,
      globalIssues,
      batchSummary: {
        totalRules: rules.length,
        validRules,
        invalidRules,
        warningCount,
        errorCount
      }
    }
  }

  // ===== PRIVATE VALIDATION HELPERS =====

  private async validateRuleSemantics(
    ruleType: string,
    rules: any
  ): Promise<{ issues: string[]; suggestions: string[] }> {
    const issues: string[] = []
    const suggestions: string[] = []

    switch (ruleType) {
      case 'referrer':
        if (rules.domains && rules.domains.length === 0) {
          issues.push('Referrer rule must specify at least one domain')
        }
        if (rules.domains && rules.domains.some((d: string) => !d.includes('.'))) {
          issues.push('Invalid domain format detected in referrer rule')
          suggestions.push('Ensure all domains include a TLD (e.g., .com, .org)')
        }
        break

      case 'location':
        if (!rules.countries && !rules.regions && !rules.cities) {
          issues.push('Location rule must specify at least one location criteria')
        }
        if (rules.countries && rules.excludeCountries) {
          const overlap = rules.countries.filter((c: string) => rules.excludeCountries.includes(c))
          if (overlap.length > 0) {
            issues.push('Country inclusion and exclusion lists overlap')
          }
        }
        break

      case 'device':
        if (!rules.deviceTypes && !rules.platforms && !rules.browsers) {
          issues.push('Device rule must specify at least one device criteria')
        }
        break

      case 'time':
        if (rules.startTime && rules.endTime && rules.startTime >= rules.endTime) {
          issues.push('Time rule start time must be before end time')
        }
        if (rules.daysOfWeek && rules.daysOfWeek.some((d: number) => d < 0 || d > 6)) {
          issues.push('Invalid day of week values (must be 0-6)')
        }
        break
    }

    return { issues, suggestions }
  }

  private validateRulePerformance(rules: any): { warnings: string[]; suggestions: string[] } {
    const warnings: string[] = []
    const suggestions: string[] = []

    // Check for potentially expensive operations
    if (rules.matchType === 'regex') {
      warnings.push('Regex matching can impact performance')
      suggestions.push('Consider using exact or contains matching for better performance')
    }

    if (rules.domains && rules.domains.length > 10) {
      warnings.push('Large number of domains may impact evaluation performance')
      suggestions.push('Consider consolidating domains or using pattern matching')
    }

    if (rules.countries && rules.countries.length > 20) {
      warnings.push('Large number of countries may impact evaluation performance')
      suggestions.push('Consider using region-based rules instead')
    }

    return { warnings, suggestions }
  }

  private validateRuleSecurity(rules: any): { concerns: string[]; suggestions: string[] } {
    const concerns: string[] = []
    const suggestions: string[] = []

    // Check for potential security issues
    if (rules.matchType === 'regex') {
      // Basic regex security check
      const regexPatterns = rules.domains || []
      for (const pattern of regexPatterns) {
        if (typeof pattern === 'string' && pattern.includes('.*.*')) {
          concerns.push('Potentially unsafe regex pattern detected')
          suggestions.push('Avoid overly broad regex patterns that could cause ReDoS')
        }
      }
    }

    // Check for overly permissive rules
    if (rules.domains && rules.domains.includes('*')) {
      concerns.push('Wildcard domain matching detected')
      suggestions.push('Specify explicit domains for better security')
    }

    return { concerns, suggestions }
  }

  private validateRuleCompatibility(
    newRule: Partial<EnhancedLinkCondition>,
    existingRules: EnhancedLinkCondition[]
  ): { issues: string[]; suggestions: string[] } {
    const issues: string[] = []
    const suggestions: string[] = []

    // Check for priority conflicts
    const samePriorityRules = existingRules.filter(r => r.priority === newRule.priority)
    if (samePriorityRules.length > 0) {
      issues.push('Rule priority conflicts with existing rules')
      suggestions.push('Use a unique priority value or adjust existing rule priorities')
    }

    // Check for rule type compatibility
    const sameTypeRules = existingRules.filter(r => r.type === newRule.type)
    if (sameTypeRules.length > 5) {
      issues.push(`Many rules of type '${newRule.type}' already exist`)
      suggestions.push('Consider consolidating similar rules for better maintainability')
    }

    return { issues, suggestions }
  }

  private shouldApplyResolution(resolution: ConflictResolution, strategy: string): boolean {
    switch (strategy) {
      case 'conservative':
        return resolution.automatic && resolution.impact === 'low'
      case 'aggressive':
        return resolution.automatic
      case 'balanced':
      default:
        return resolution.automatic && (resolution.impact === 'low' || resolution.impact === 'medium')
    }
  }

  private generateResolutionSummary(
    resolved: ConflictResolution[],
    unresolved: ConflictResolution[]
  ): string {
    const resolvedCount = resolved.length
    const unresolvedCount = unresolved.length
    const totalCount = resolvedCount + unresolvedCount

    if (totalCount === 0) {
      return 'No conflicts detected'
    }

    let summary = `Processed ${totalCount} conflicts: `

    if (resolvedCount > 0) {
      summary += `${resolvedCount} automatically resolved`
    }

    if (unresolvedCount > 0) {
      if (resolvedCount > 0) summary += ', '
      summary += `${unresolvedCount} require manual attention`
    }

    return summary
  }

  private async analyzePerformanceOptimizations(
    link: ConditionalLinkWithConditions
  ): Promise<{ suggestions: any[]; scorePenalty: number }> {
    const suggestions: any[] = []
    let scorePenalty = 0

    // Check rule complexity
    const complexRules = link.conditions.filter(c => this.isComplexRule(c))
    if (complexRules.length > 0) {
      suggestions.push({
        category: 'performance',
        priority: 'medium',
        title: 'Simplify complex rules',
        description: `${complexRules.length} rules have high complexity`,
        impact: 'Faster rule evaluation',
        effort: 'medium',
        autoApplicable: false,
        estimatedGain: 15
      })
      scorePenalty += 10
    }

    // Check rule count
    if (link.conditions.length > 10) {
      suggestions.push({
        category: 'performance',
        priority: 'high',
        title: 'Reduce number of rules',
        description: `${link.conditions.length} rules may impact performance`,
        impact: 'Significantly faster evaluation',
        effort: 'high',
        autoApplicable: false,
        estimatedGain: 25
      })
      scorePenalty += 15
    }

    return { suggestions, scorePenalty }
  }

  private analyzeMaintainabilityOptimizations(
    link: ConditionalLinkWithConditions
  ): { suggestions: any[]; scorePenalty: number } {
    const suggestions: any[] = []
    let scorePenalty = 0

    // Check for duplicate rules
    const duplicates = this.findRedundantRules(link.conditions)
    if (duplicates.length > 0) {
      suggestions.push({
        category: 'maintainability',
        priority: 'medium',
        title: 'Remove duplicate rules',
        description: `${duplicates.length} duplicate rules found`,
        impact: 'Easier rule management',
        effort: 'low',
        autoApplicable: true,
        estimatedGain: 10
      })
      scorePenalty += 5
    }

    // Check priority gaps
    const priorities = link.conditions.map(c => c.priority).sort((a, b) => a - b)
    const hasGaps = priorities.some((p, i) => i > 0 && p - priorities[i - 1] > 10)
    if (hasGaps) {
      suggestions.push({
        category: 'maintainability',
        priority: 'low',
        title: 'Optimize rule priorities',
        description: 'Large gaps in priority values detected',
        impact: 'Better rule organization',
        effort: 'low',
        autoApplicable: true,
        estimatedGain: 5
      })
      scorePenalty += 2
    }

    return { suggestions, scorePenalty }
  }

  private analyzeReliabilityOptimizations(
    link: ConditionalLinkWithConditions
  ): { suggestions: any[]; scorePenalty: number } {
    const suggestions: any[] = []
    let scorePenalty = 0

    // Check for conflicting rules
    const conflictResult = RuleConflictResolver.detectConflicts(link)
    if (conflictResult.hasConflicts) {
      const errorConflicts = conflictResult.conflicts.filter(c => c.severity === 'error')
      if (errorConflicts.length > 0) {
        suggestions.push({
          category: 'reliability',
          priority: 'critical',
          title: 'Resolve rule conflicts',
          description: `${errorConflicts.length} critical conflicts detected`,
          impact: 'Prevent unpredictable behavior',
          effort: 'medium',
          autoApplicable: true,
          estimatedGain: 30
        })
        scorePenalty += 20
      }
    }

    return { suggestions, scorePenalty }
  }

  private analyzeSecurityOptimizations(
    link: ConditionalLinkWithConditions
  ): { suggestions: any[]; scorePenalty: number } {
    const suggestions: any[] = []
    let scorePenalty = 0

    // Check for overly permissive rules
    const permissiveRules = link.conditions.filter(c => this.isPermissiveRule(c))
    if (permissiveRules.length > 0) {
      suggestions.push({
        category: 'security',
        priority: 'medium',
        title: 'Tighten rule restrictions',
        description: `${permissiveRules.length} overly permissive rules found`,
        impact: 'Better security posture',
        effort: 'medium',
        autoApplicable: false,
        estimatedGain: 10
      })
      scorePenalty += 8
    }

    return { suggestions, scorePenalty }
  }

  private detectCrossRuleIssues(
    ruleData: Partial<EnhancedLinkCondition>,
    allRules: Array<{ linkId: string; ruleData: Partial<EnhancedLinkCondition> }>
  ): string[] {
    const issues: string[] = []

    // Check for duplicate priorities across different links
    const samePriorityRules = allRules.filter(r =>
      r.ruleData.priority === ruleData.priority && r.linkId !== ruleData.linkId
    )

    if (samePriorityRules.length > 0) {
      issues.push('Priority value used in other links - consider using unique priorities')
    }

    return issues
  }

  private detectGlobalPatterns(
    rules: Array<{ linkId: string; ruleData: Partial<EnhancedLinkCondition> }>
  ): string[] {
    const issues: string[] = []

    // Check for common anti-patterns
    const ruleTypes = rules.map(r => r.ruleData.type)
    const typeCount = ruleTypes.reduce((acc, type) => {
      acc[type || 'unknown'] = (acc[type || 'unknown'] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    if (typeCount.referrer && typeCount.referrer > rules.length * 0.8) {
      issues.push('High concentration of referrer rules - consider diversifying rule types')
    }

    return issues
  }

  private isComplexRule(rule: EnhancedLinkCondition): boolean {
    // Simple complexity heuristic
    const rulesString = JSON.stringify(rule.rules)
    return rulesString.length > 500 ||
           (rule.rules as any).matchType === 'regex' ||
           (Array.isArray((rule.rules as any).domains) && (rule.rules as any).domains.length > 10)
  }

  private isPermissiveRule(rule: EnhancedLinkCondition): boolean {
    // Check for overly broad rules
    const rules = rule.rules as any

    if (rule.type === 'referrer' && rules.domains) {
      return rules.domains.includes('*') || rules.domains.some((d: string) => d === '')
    }

    if (rule.type === 'location' && rules.countries) {
      return rules.countries.length > 50 // Very broad location targeting
    }

    return false
  }
}
